<?php
require_once __DIR__ . '/../config/database.php';

class SiswaPeriode {
    private $conn;
    private $table_name = "siswa_periode";
    private $siswa_table = "siswa";

    public $id;
    public $siswa_id;
    public $kelas_id;
    public $tahun_ajaran;
    public $semester;
    public $status;
    public $tanggal_mulai;
    public $tanggal_selesai;
    public $is_current;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Get students by academic period
     */
    public function getSiswaByPeriode($tahun_ajaran, $semester, $kelas_id = null) {
        $query = "SELECT
                    s.id as siswa_id,
                    s.nis,
                    s.nama_siswa,
                    s.jenis_kelamin,
                    s.alamat,
                    s.no_telp,
                    sp.kelas_id,
                    k.nama_kelas,
                    sp.tahun_ajaran,
                    sp.semester,
                    sp.status,
                    sp.is_current
                FROM " . $this->siswa_table . " s
                JOIN " . $this->table_name . " sp ON s.id = sp.siswa_id
                JOIN kelas k ON sp.kelas_id = k.id
                WHERE sp.tahun_ajaran = :tahun_ajaran
                AND sp.semester = :semester
                AND sp.is_current = 1";

        if ($kelas_id) {
            $query .= " AND sp.kelas_id = :kelas_id";
        }

        $query .= " ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':semester', $semester);
        
        if ($kelas_id) {
            $stmt->bindParam(':kelas_id', $kelas_id);
        }
        
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get all periods for a specific student
     */
    public function getPeriodesBySiswa($siswa_id) {
        $query = "SELECT 
                    sp.*,
                    k.nama_kelas,
                    s.nama_siswa,
                    s.nis
                FROM " . $this->table_name . " sp
                JOIN kelas k ON sp.kelas_id = k.id
                JOIN " . $this->siswa_table . " s ON sp.siswa_id = s.id
                WHERE sp.siswa_id = :siswa_id
                ORDER BY sp.tahun_ajaran DESC, sp.semester DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get current period for a student
     */
    public function getCurrentPeriodeBySiswa($siswa_id) {
        $query = "SELECT 
                    sp.*,
                    k.nama_kelas,
                    s.nama_siswa,
                    s.nis
                FROM " . $this->table_name . " sp
                JOIN kelas k ON sp.kelas_id = k.id
                JOIN " . $this->siswa_table . " s ON sp.siswa_id = s.id
                WHERE sp.siswa_id = :siswa_id
                AND sp.is_current = 1
                LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get students by academic period and class
     */
    public function getSiswaByPeriodeAndKelas($tahun_ajaran, $semester, $kelas_id) {
        $query = "SELECT
                    s.id as siswa_id,
                    s.nis,
                    s.nama_siswa,
                    s.jenis_kelamin,
                    s.alamat,
                    s.no_telp,
                    sp.kelas_id,
                    k.nama_kelas,
                    sp.tahun_ajaran,
                    sp.semester,
                    sp.status,
                    sp.is_current
                FROM " . $this->siswa_table . " s
                JOIN " . $this->table_name . " sp ON s.id = sp.siswa_id
                JOIN kelas k ON sp.kelas_id = k.id
                WHERE sp.tahun_ajaran = :tahun_ajaran
                AND sp.semester = :semester
                AND sp.kelas_id = :kelas_id
                AND sp.is_current = 1
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Deactivate current period for a student
     */
    public function deactivateCurrentPeriod($siswa_id) {
        $query = "UPDATE " . $this->table_name . "
                 SET is_current = 0
                 WHERE siswa_id = :siswa_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":siswa_id", $siswa_id);

        return $stmt->execute();
    }

    /**
     * Add student to a new academic period
     */
    public function addSiswaToNewPeriode($siswa_id, $kelas_id, $tahun_ajaran, $semester) {
        try {
            $this->conn->beginTransaction();

            // Mark previous periods as not current
            $query = "UPDATE " . $this->table_name . " 
                     SET is_current = 0 
                     WHERE siswa_id = :siswa_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':siswa_id', $siswa_id);
            $stmt->execute();

            // Add new period
            $query = "INSERT INTO " . $this->table_name . " 
                     (siswa_id, kelas_id, tahun_ajaran, semester, is_current, status)
                     VALUES (:siswa_id, :kelas_id, :tahun_ajaran, :semester, 1, 'aktif')";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':siswa_id', $siswa_id);
            $stmt->bindParam(':kelas_id', $kelas_id);
            $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
            $stmt->bindParam(':semester', $semester);
            $stmt->execute();

            // Update siswa current period reference
            $query = "UPDATE " . $this->siswa_table . " 
                     SET kelas_id = :kelas_id,
                         tahun_ajaran_current = :tahun_ajaran,
                         semester_current = :semester
                     WHERE id = :siswa_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':siswa_id', $siswa_id);
            $stmt->bindParam(':kelas_id', $kelas_id);
            $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
            $stmt->bindParam(':semester', $semester);
            $stmt->execute();

            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }

    /**
     * Get available academic years and semesters
     */
    public function getAvailablePeriodes() {
        $query = "SELECT DISTINCT tahun_ajaran, semester
                 FROM " . $this->table_name . "
                 ORDER BY tahun_ajaran DESC, semester DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get student data by NIS for a specific period
     */
    public function getSiswaByNISAndPeriode($nis, $tahun_ajaran, $semester) {
        $query = "SELECT
                    s.*,
                    sp.kelas_id,
                    k.nama_kelas,
                    sp.tahun_ajaran,
                    sp.semester,
                    sp.status
                FROM " . $this->siswa_table . " s
                JOIN " . $this->table_name . " sp ON s.id = sp.siswa_id
                JOIN kelas k ON sp.kelas_id = k.id
                WHERE s.nis = :nis
                AND sp.tahun_ajaran = :tahun_ajaran
                AND sp.semester = :semester
                LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':nis', $nis);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':semester', $semester);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get students who don't have any period assignments
     */
    public function getSiswaWithoutPeriods($kelas_id = null) {
        $query = "SELECT
                    s.id as siswa_id,
                    s.nis,
                    s.nama_siswa,
                    s.jenis_kelamin,
                    s.alamat,
                    s.no_telp,
                    s.kelas_id,
                    k.nama_kelas
                FROM " . $this->siswa_table . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                LEFT JOIN " . $this->table_name . " sp ON s.id = sp.siswa_id
                WHERE sp.siswa_id IS NULL";

        if ($kelas_id) {
            $query .= " AND s.kelas_id = :kelas_id";
        }

        $query .= " ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);

        if ($kelas_id) {
            $stmt->bindParam(':kelas_id', $kelas_id);
        }

        $stmt->execute();
        return $stmt;
    }

    /**
     * Get count of students without period assignments
     */
    public function getCountSiswaWithoutPeriods($kelas_id = null) {
        $query = "SELECT COUNT(*) as total
                FROM " . $this->siswa_table . " s
                LEFT JOIN " . $this->table_name . " sp ON s.id = sp.siswa_id
                WHERE sp.siswa_id IS NULL";

        if ($kelas_id) {
            $query .= " AND s.kelas_id = :kelas_id";
        }

        $stmt = $this->conn->prepare($query);

        if ($kelas_id) {
            $stmt->bindParam(':kelas_id', $kelas_id);
        }

        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'];
    }

    /**
     * Update period status (e.g., mark as graduated)
     */
    public function updatePeriodeStatus($siswa_id, $tahun_ajaran, $semester, $status) {
        $query = "UPDATE " . $this->table_name . "
                 SET status = :status
                 WHERE siswa_id = :siswa_id
                 AND tahun_ajaran = :tahun_ajaran
                 AND semester = :semester";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':status', $status);
        return $stmt->execute();
    }

    /**
     * Get count of students by period
     */
    public function getCountByPeriode($tahun_ajaran, $semester, $kelas_id = null) {
        $query = "SELECT COUNT(*) as total
                 FROM " . $this->table_name . " sp
                 WHERE sp.tahun_ajaran = :tahun_ajaran
                 AND sp.semester = :semester";
        
        if ($kelas_id) {
            $query .= " AND sp.kelas_id = :kelas_id";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':semester', $semester);
        
        if ($kelas_id) {
            $stmt->bindParam(':kelas_id', $kelas_id);
        }
        
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    /**
     * Check if student exists in a specific period
     */
    public function existsInPeriode($siswa_id, $tahun_ajaran, $semester) {
        $query = "SELECT COUNT(*) as count
                 FROM " . $this->table_name . "
                 WHERE siswa_id = :siswa_id
                 AND tahun_ajaran = :tahun_ajaran
                 AND semester = :semester";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':semester', $semester);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['count'] > 0;
    }
}
